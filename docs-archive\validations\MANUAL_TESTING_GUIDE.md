# 🧪 Guía de Pruebas Manuales - Óptica VisualCare

## 📋 **Estado Actual**

✅ **Organización Creada**: Óptica VisualCare
🆔 **ID**: `927cecbe-d9e5-43a4-b9d0-25f942ededc4`
🔗 **Slug**: `visualcare`
📧 **Email**: `<EMAIL>`
✅ **Servicios Configurados**: 8 servicios ópticos disponibles
🏥 **Categorías**: <PERSON><PERSON><PERSON><PERSON>, Lentes de Contacto, Diagnóstico Avanzado, Especializada, Terapia

## 👥 **Usuarios para Crear Manualmente**

### **🔑 Credenciales de Acceso**
**Contraseña para todos**: `VisualCare2025!`

### **👨‍💼 Administradores**

#### **<PERSON> - Admin Principal**
- **Email**: `<EMAIL>`
- **Nombre**: <PERSON>
- **Teléfono**: +34600111222
- **Rol**: `admin`

#### **<PERSON> - Admin <PERSON>**
- **Email**: `<EMAIL>`
- **Nombre**: <PERSON>
- **Teléfono**: +34600111223
- **Rol**: `admin`

### **👩‍⚕️ Doctores/Optometristas**

#### **Ana Rodríguez - Optometría Clínica**
- **Email**: `<EMAIL>`
- **Nombre**: Ana Rodríguez
- **Teléfono**: +34600111224
- **Rol**: `doctor`
- **Especialidad**: Optometría Clínica
- **Experiencia**: 15 años
- **Tarifa**: €60.00

#### **Pedro Sánchez - Contactología**
- **Email**: `<EMAIL>`
- **Nombre**: Pedro Sánchez
- **Teléfono**: +34600111225
- **Rol**: `doctor`
- **Especialidad**: Contactología Avanzada
- **Experiencia**: 10 años
- **Tarifa**: €50.00

#### **Elena López - Optometría Pediátrica**
- **Email**: `<EMAIL>`
- **Nombre**: Elena López
- **Teléfono**: +34600111226
- **Rol**: `doctor`
- **Especialidad**: Optometría Pediátrica
- **Experiencia**: 8 años
- **Tarifa**: €45.00

#### **Miguel Fernández - Optometría General**
- **Email**: `<EMAIL>`
- **Nombre**: Miguel Fernández
- **Teléfono**: +34600111227
- **Rol**: `doctor`
- **Especialidad**: Optometría General
- **Experiencia**: 5 años
- **Tarifa**: €40.00

#### **Sofía Torres - Baja Visión**
- **Email**: `<EMAIL>`
- **Nombre**: Sofía Torres
- **Teléfono**: +34600111228
- **Rol**: `doctor`
- **Especialidad**: Baja Visión
- **Experiencia**: 12 años
- **Tarifa**: €90.00

### **👥 Personal (Staff)**

#### **Carmen Ruiz - Recepcionista**
- **Email**: `<EMAIL>`
- **Nombre**: Carmen Ruiz
- **Teléfono**: +34600111229
- **Rol**: `staff`

#### **Javier Moreno - Técnico**
- **Email**: `<EMAIL>`
- **Nombre**: Javier Moreno
- **Teléfono**: +34600111230
- **Rol**: `staff`

#### **Lucía Navarro - Asistente**
- **Email**: `<EMAIL>`
- **Nombre**: Lucía Navarro
- **Teléfono**: +34600111231
- **Rol**: `staff`

### **🏥 Pacientes**

#### **María García - Paciente 1**
- **Email**: `<EMAIL>`
- **Nombre**: María García
- **Teléfono**: +34600222111
- **Rol**: `patient`
- **Historial**: Miopía progresiva desde los 20 años
- **Contacto Emergencia**: José García (Esposo) - +34600333111

#### **Juan Pérez - Paciente 2**
- **Email**: `<EMAIL>`
- **Nombre**: Juan Pérez
- **Teléfono**: +34600222112
- **Rol**: `patient`
- **Historial**: Hipertensión controlada, revisiones anuales
- **Alergias**: Alergia al polen
- **Contacto Emergencia**: Carmen Pérez (Esposa) - +34600333112

#### **Isabel Díaz - Paciente 3**
- **Email**: `<EMAIL>`
- **Nombre**: Isabel Díaz
- **Teléfono**: +34600222113
- **Rol**: `patient`
- **Historial**: Astigmatismo leve
- **Contacto Emergencia**: Miguel Díaz (Hermano) - +34600333113

## 🚀 **Proceso de Registro Manual**

### **Paso 1: Registro de Usuario**
1. Ir a la página de registro de AgentSalud
2. Usar el email y contraseña correspondiente
3. Confirmar el email si es necesario

### **Paso 2: Configuración de Perfil**
1. Completar el perfil con los datos proporcionados
2. Seleccionar la organización: **Óptica VisualCare**
3. Asignar el rol correspondiente

### **Paso 3: Configuración Específica por Rol**

#### **Para Doctores:**
- Completar información profesional
- Número de licencia: OPT-XXX-ES
- Especialidad según la lista
- Años de experiencia
- Tarifa de consulta

#### **Para Pacientes:**
- Completar información médica
- Historial médico
- Alergias
- Contacto de emergencia

## 🏥 **Servicios Disponibles**

### **📋 Exámenes**
1. **Examen Visual Completo** - €60.00 (45 min)
   - Evaluación completa de la salud visual y ocular
2. **Control Visual Rápido** - €30.00 (20 min)
   - Revisión básica de graduación
3. **Examen Visual Pediátrico** - €45.00 (30 min)
   - Evaluación especializada para niños

### **👁️ Lentes de Contacto**
4. **Adaptación de Lentes Blandas** - €50.00 (40 min)
5. **Adaptación de Lentes Rígidas** - €80.00 (60 min)

### **🔬 Diagnóstico Avanzado**
6. **Topografía Corneal** - €70.00 (30 min)

### **🎯 Especializada**
7. **Evaluación de Baja Visión** - €90.00 (60 min)

### **💪 Terapia**
8. **Terapia Visual** - €55.00 (45 min)

## 🧪 **Escenarios de Prueba Recomendados**

### **1. Pruebas de Autenticación**
- ✅ Registro de nuevos usuarios
- ✅ Login con diferentes roles
- ✅ Verificación de permisos por rol

### **2. Pruebas de AI Chatbot**
- ✅ "Necesito una cita con optometría"
- ✅ "Quiero agendar un examen visual completo"
- ✅ "Necesito una adaptación de lentes de contacto"
- ✅ "Quiero una topografía corneal"
- ✅ "Necesito cambiar mi cita del martes"
- ✅ "¿Qué citas tengo programadas?"

### **3. Pruebas de Reserva de Citas**
- ✅ Paciente agenda cita con doctor específico
- ✅ Selección de servicios específicos
- ✅ Doctor ve sus citas programadas
- ✅ Admin gestiona todas las citas
- ✅ Cancelación y reprogramación

### **4. Pruebas Multi-Tenant**
- ✅ Usuarios solo ven datos de su organización
- ✅ No pueden acceder a otras organizaciones
- ✅ Roles funcionan correctamente

### **5. Pruebas de Dashboard**
- ✅ Vista de calendario por rol
- ✅ Estadísticas de citas
- ✅ Gestión de horarios (doctores)

## 🔧 **Comandos Útiles para Testing**

### **Verificar Organización**
```sql
SELECT * FROM organizations WHERE slug = 'visualcare';
```

### **Ver Usuarios Registrados**
```sql
SELECT p.first_name, p.last_name, p.email, p.role
FROM profiles p
WHERE p.organization_id = '927cecbe-d9e5-43a4-b9d0-25f942ededc4';
```

### **Ver Doctores**
```sql
SELECT p.first_name, p.last_name, d.specialization, d.consultation_fee
FROM doctors d
JOIN profiles p ON d.profile_id = p.id
WHERE d.organization_id = '927cecbe-d9e5-43a4-b9d0-25f942ededc4';
```

### **Ver Servicios**
```sql
SELECT name, description, duration_minutes, price, category
FROM services
WHERE organization_id = '927cecbe-d9e5-43a4-b9d0-25f942ededc4'
ORDER BY category, name;
```

### **Ver Citas**
```sql
SELECT a.appointment_date, a.appointment_time,
       pp.first_name as patient_name,
       pd.first_name as doctor_name,
       a.status
FROM appointments a
JOIN patients pt ON a.patient_id = pt.id
JOIN profiles pp ON pt.profile_id = pp.id
JOIN doctors d ON a.doctor_id = d.id
JOIN profiles pd ON d.profile_id = pd.id
WHERE a.organization_id = '927cecbe-d9e5-43a4-b9d0-25f942ededc4';
```

## 📝 **Notas Importantes**

1. **Contraseña Única**: Todos los usuarios usan `VisualCare2025!`
2. **Organización ID**: `927cecbe-d9e5-43a4-b9d0-25f942ededc4`
3. **Slug**: `visualcare`
4. **Emails Únicos**: Cada usuario tiene un email único para evitar conflictos
5. **Roles Definidos**: admin, doctor, staff, patient

## 🎯 **Objetivos de Testing**

- ✅ Validar flujo completo de registro y autenticación
- ✅ Probar funcionalidad AI de reserva de citas
- ✅ Verificar multi-tenancy y seguridad
- ✅ Confirmar roles y permisos
- ✅ Testear interfaz de usuario y UX

---

**Última Actualización**: Enero 2025
**Estado**: ✅ Organización creada, lista para registro manual de usuarios
