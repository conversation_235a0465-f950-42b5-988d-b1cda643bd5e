/**
 * FRONTEND AVAILABILITY VALIDATION SCRIPT
 * 
 * This script validates that the frontend availability inconsistencies have been
 * resolved by testing both flows with identical parameters and comparing results.
 * 
 * Run this in the browser console while on the appointment booking page.
 * 
 * <AUTHOR> MVP Team - Critical Frontend Validation
 * @version 1.0.0
 */

console.log('🚀 FRONTEND AVAILABILITY VALIDATION SCRIPT');
console.log('='.repeat(60));

/**
 * Test parameters for validation
 */
const testParams = {
  organizationId: 'test-org-123', // Replace with actual org ID
  serviceId: 'test-service-456',   // Replace with actual service ID
  startDate: '2025-06-01',
  endDate: '2025-06-07',
  userRole: 'patient',
  useStandardRules: 'false'
};

/**
 * Validate API consistency
 */
async function validateAPIConsistency() {
  console.log('\n🔍 Phase 1: API Consistency Validation');
  console.log('-'.repeat(40));
  
  try {
    // Make multiple API calls with identical parameters
    const calls = [];
    for (let i = 0; i < 3; i++) {
      const queryString = new URLSearchParams(testParams).toString();
      const response = await fetch(`/api/appointments/availability?${queryString}`);
      
      if (!response.ok) {
        throw new Error(`API call ${i + 1} failed: ${response.status}`);
      }
      
      const data = await response.json();
      if (!data.success) {
        throw new Error(`API call ${i + 1} returned error: ${data.error}`);
      }
      
      calls.push(data.data);
      console.log(`✅ API Call ${i + 1}: Success`);
    }
    
    // Compare results
    const firstCall = calls[0];
    const dates = Object.keys(firstCall);
    let allConsistent = true;
    
    for (const date of dates) {
      const firstData = firstCall[date];
      
      for (let i = 1; i < calls.length; i++) {
        const otherData = calls[i][date];
        
        if (firstData.availableSlots !== otherData.availableSlots) {
          console.log(`❌ INCONSISTENCY: ${date} - Call 1: ${firstData.availableSlots}, Call ${i + 1}: ${otherData.availableSlots}`);
          allConsistent = false;
        }
      }
    }
    
    if (allConsistent) {
      console.log('✅ API CONSISTENCY: All calls return identical results');
    } else {
      console.log('❌ API CONSISTENCY: Inconsistencies detected');
    }
    
    return { success: allConsistent, data: firstCall };
    
  } catch (error) {
    console.error('❌ API VALIDATION FAILED:', error.message);
    return { success: false, error: error.message };
  }
}

/**
 * Simulate component data processing
 */
function simulateComponentProcessing(apiData) {
  console.log('\n🔍 Phase 2: Component Processing Validation');
  console.log('-'.repeat(40));
  
  const dayNames = ['Domingo', 'Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado'];
  
  // Simulate UnifiedAppointmentFlow processing
  const unifiedFlowData = [];
  for (const dateString of Object.keys(apiData)) {
    const dayData = apiData[dateString];
    const availableSlots = dayData?.availableSlots || 0;
    
    let availabilityLevel = 'none';
    if (availableSlots === 0) availabilityLevel = 'none';
    else if (availableSlots <= 2) availabilityLevel = 'low';
    else if (availableSlots <= 5) availabilityLevel = 'medium';
    else availabilityLevel = 'high';
    
    unifiedFlowData.push({
      date: dateString,
      slotsCount: availableSlots,
      availabilityLevel,
      source: 'UnifiedAppointmentFlow'
    });
  }
  
  // Simulate AIEnhancedRescheduleModal processing
  const rescheduleModalData = [];
  for (const dateStr of Object.keys(apiData)) {
    const dayData = apiData[dateStr];
    const slotsCount = dayData?.availableSlots || 0;
    
    let availabilityLevel = 'none';
    if (slotsCount === 0) availabilityLevel = 'none';
    else if (slotsCount <= 2) availabilityLevel = 'low';
    else if (slotsCount <= 5) availabilityLevel = 'medium';
    else availabilityLevel = 'high';
    
    rescheduleModalData.push({
      date: dateStr,
      slotsCount,
      availabilityLevel,
      source: 'AIEnhancedRescheduleModal'
    });
  }
  
  // Simulate WeeklyAvailabilitySelector processing (FIXED)
  const weeklyAvailabilityData = [];
  for (const dateStr of Object.keys(apiData)) {
    const dayData = apiData[dateStr];
    const slotsCount = dayData?.availableSlots || 0; // CRITICAL FIX: Use pre-calculated value
    
    let availabilityLevel = 'none';
    if (slotsCount === 0) availabilityLevel = 'none';
    else if (slotsCount <= 2) availabilityLevel = 'low';
    else if (slotsCount <= 5) availabilityLevel = 'medium';
    else availabilityLevel = 'high';
    
    weeklyAvailabilityData.push({
      date: dateStr,
      slotsCount,
      availabilityLevel,
      source: 'WeeklyAvailabilitySelector'
    });
  }
  
  // Compare all three processing methods
  let allConsistent = true;
  const comparisonResults = [];
  
  for (let i = 0; i < unifiedFlowData.length; i++) {
    const unified = unifiedFlowData[i];
    const reschedule = rescheduleModalData[i];
    const weekly = weeklyAvailabilityData[i];
    
    const isConsistent = (
      unified.slotsCount === reschedule.slotsCount &&
      unified.slotsCount === weekly.slotsCount &&
      unified.availabilityLevel === reschedule.availabilityLevel &&
      unified.availabilityLevel === weekly.availabilityLevel
    );
    
    if (!isConsistent) {
      allConsistent = false;
      console.log(`❌ INCONSISTENCY on ${unified.date}:`);
      console.log(`   UnifiedFlow: ${unified.slotsCount} slots (${unified.availabilityLevel})`);
      console.log(`   RescheduleModal: ${reschedule.slotsCount} slots (${reschedule.availabilityLevel})`);
      console.log(`   WeeklyAvailability: ${weekly.slotsCount} slots (${weekly.availabilityLevel})`);
    } else {
      console.log(`✅ CONSISTENT on ${unified.date}: ${unified.slotsCount} slots (${unified.availabilityLevel})`);
    }
    
    comparisonResults.push({
      date: unified.date,
      consistent: isConsistent,
      unified,
      reschedule,
      weekly
    });
  }
  
  if (allConsistent) {
    console.log('✅ COMPONENT PROCESSING: All components process data identically');
  } else {
    console.log('❌ COMPONENT PROCESSING: Inconsistencies detected between components');
  }
  
  return { success: allConsistent, results: comparisonResults };
}

/**
 * Check for console logs from components
 */
function checkComponentLogs() {
  console.log('\n🔍 Phase 3: Component Logging Validation');
  console.log('-'.repeat(40));
  console.log('📋 Look for these debug logs in the console:');
  console.log('   🔄 NEW APPOINTMENT FLOW: Loading availability...');
  console.log('   🔄 RESCHEDULE MODAL: Loading availability...');
  console.log('   ✅ WEEKLY AVAILABILITY SELECTOR: Real availability data loaded');
  console.log('');
  console.log('💡 If you see these logs, the fixed components are being executed.');
  console.log('💡 Compare the logged data to verify consistency.');
}

/**
 * Main validation function
 */
async function runValidation() {
  const startTime = Date.now();
  
  console.log('🎯 Starting comprehensive frontend validation...');
  
  // Phase 1: API Consistency
  const apiResult = await validateAPIConsistency();
  if (!apiResult.success) {
    console.log('\n❌ VALIDATION FAILED: API layer issues detected');
    return;
  }
  
  // Phase 2: Component Processing
  const componentResult = simulateComponentProcessing(apiResult.data);
  
  // Phase 3: Component Logging
  checkComponentLogs();
  
  // Summary
  const duration = Date.now() - startTime;
  console.log('\n' + '='.repeat(60));
  console.log('📊 VALIDATION SUMMARY:');
  console.log(`   API Consistency: ${apiResult.success ? '✅ PASSED' : '❌ FAILED'}`);
  console.log(`   Component Processing: ${componentResult.success ? '✅ PASSED' : '❌ FAILED'}`);
  
  const overallSuccess = apiResult.success && componentResult.success;
  
  if (overallSuccess) {
    console.log('\n🎉 VALIDATION PASSED: Frontend availability inconsistencies are RESOLVED!');
    console.log('✅ All components now process availability data identically');
    console.log('✅ WeeklyAvailabilitySelector fix is working correctly');
  } else {
    console.log('\n❌ VALIDATION FAILED: Issues still exist');
    console.log('🔧 Review the detailed logs above for specific problems');
  }
  
  console.log(`⏱️  Validation completed in ${duration}ms`);
  console.log('='.repeat(60));
  
  return overallSuccess;
}

/**
 * Instructions for manual testing
 */
function showManualTestingInstructions() {
  console.log('\n📋 MANUAL TESTING INSTRUCTIONS:');
  console.log('='.repeat(60));
  console.log('1. Open the appointment booking page');
  console.log('2. Navigate to the new appointment flow');
  console.log('3. Note the availability numbers for each date');
  console.log('4. Open an existing appointment for rescheduling');
  console.log('5. Compare the availability numbers - they should be IDENTICAL');
  console.log('6. Check browser console for debug logs confirming the fix');
  console.log('');
  console.log('🎯 Expected Result: Both flows show identical slot counts');
  console.log('✅ If numbers match: Fix is working correctly');
  console.log('❌ If numbers differ: Additional investigation needed');
}

// Auto-run validation if this script is executed
if (typeof window !== 'undefined') {
  console.log('🌐 Browser environment detected - running validation...');
  runValidation().then(success => {
    if (!success) {
      showManualTestingInstructions();
    }
  });
} else {
  console.log('📝 Node.js environment - showing manual testing instructions');
  showManualTestingInstructions();
}

// Export functions for manual use
if (typeof window !== 'undefined') {
  window.validateAvailability = runValidation;
  window.showTestingInstructions = showManualTestingInstructions;
  console.log('\n💡 Available functions:');
  console.log('   validateAvailability() - Run full validation');
  console.log('   showTestingInstructions() - Show manual testing steps');
}
